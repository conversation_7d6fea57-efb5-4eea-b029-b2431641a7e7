import { rbacRequest } from '@/request';
import type { Unit } from '@/service/system';
import { formatTree } from 'yth-ui/es/components/util/treeList';

/**
 * API 响应格式
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
};

/**
 * 组织机构树查询参数
 */
export interface UnitTreeQueryParams {
  unitId?: string;
  unitType?: string;
  includeDisabled?: boolean;
  maxDepth?: number;
  [key: string]: unknown;
}

/**
 * 字典数据项
 */
export interface DictionaryItem {
  /** 子项列表 */
  children: DictionaryItem[];
  /** 字典编码 */
  code: string;
  /** 父级编码 */
  fatherCode: string;
  /** 过滤值 */
  filterVal: string;
  /** 唯一标识 */
  id: string;
  /** 是否为子项 */
  isSon: boolean;
  /** 是否停用 */
  isStop: boolean;
  /** 组织ID */
  orgId: string;
  /** 备注 */
  remark: string;
  /** 排序 */
  sort: number;
  /** 显示文本 */
  text: string;
}

interface BaseApiInterface {
  getUnitTree: (params?: UnitTreeQueryParams) => Promise<Unit>;
  getDictionary: () => Promise<DictionaryItem[]>;
}

/**
 * 基础API服务
 */
const baseApi: BaseApiInterface = {
  /**
   * 获取组织机构树
   */
  getUnitTree: async (params: UnitTreeQueryParams = {}): Promise<Unit> => {
    const resp: ApiResponse<Unit> = await rbacRequest.get('/unit/unitTree', {
      params,
    });

    return formatTree(resp.data, 'unitType', 'unitName');
  },

  /**
   * 获取字典数据
   */
  getDictionary: async (): Promise<DictionaryItem[]> => {
    const resp: ApiResponse<DictionaryItem[]> = await rbacRequest.get('/dataDictionary/getDic');

    return resp.data;
  },
};

export default baseApi;
