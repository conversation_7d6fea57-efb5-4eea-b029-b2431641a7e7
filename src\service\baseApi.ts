import { rbacRequest } from '@/request';
import type { Unit } from '@/service/system';
import { formatTree } from 'yth-ui/es/components/util/treeList';

/**
 * API 响应格式
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
};

/**
 * 组织机构树查询参数
 */
export interface UnitTreeQueryParams {
  unitId?: string;
  unitType?: string;
  includeDisabled?: boolean;
  maxDepth?: number;
  [key: string]: unknown;
}

interface BaseApiInterface {
  getUnitTree: (params?: UnitTreeQueryParams) => Promise<Unit>;
}

/**
 * 基础API服务
 */
const baseApi: BaseApiInterface = {
  /**
   * 获取组织机构树
   */
  getUnitTree: async (params: UnitTreeQueryParams = {}): Promise<Unit> => {
    const resp: ApiResponse<Unit> = await rbacRequest.get('/unit/unitTree', {
      params,
    });

    return formatTree(resp.data, 'unitType', 'unitName');
  },
};

export default baseApi;
