import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import assessmentApi from '@/service/assessmentApi';
import type {
  AssessmentPersonnel,
  ApiResponse,
  AssessmentPersonnelQueryParams,
} from '@/service/assessmentApi';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import systemApi, { Unit } from '@/service/system';
import locales from '@/locales';
import moment from 'moment';
import AssessmentPersonnelModal from './components/AssessmentPersonnelModal';

// 筛选条件类型
type FilterType = {
  planCode?: string;
  planName?: string;
  companyId?: string;
  trainingType?: { code: string; text: string }[];
  trainingName?: string;
};

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC = () => {
  const actionRef: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();
  const [dataObj, setDataObj] = useState<AssessmentPersonnel | undefined>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalMode, setModalMode] = useState<'add' | 'edit' | 'view'>('add');

  /**
   * 处理筛选条件
   * @param filter - 筛选条件
   * @returns 处理后的筛选条件
   */
  const handleFilter: (filter: FilterType) => AssessmentPersonnelQueryParams = (
    filter: FilterType,
  ) => {
    const result: AssessmentPersonnelQueryParams = {};

    if (filter.planCode) {
      result.planCode = filter.planCode;
    }
    if (filter.planName) {
      result.planName = filter.planName;
    }
    if (filter.companyId) {
      result.companyId = filter.companyId;
    }
    if (filter.trainingType && filter.trainingType.length > 0) {
      result.trainingType = filter.trainingType[0].code;
    }
    if (filter.trainingName) {
      result.trainingName = filter.trainingName;
    }

    return result;
  };

  /**
   * 关闭弹窗
   */
  const closeModal: () => void = () => {
    setModalVisible(false);
    setDataObj(undefined);
    setModalMode('add');
    // 刷新列表
    if (actionRef.current) {
      actionRef.current.reload({});
    }
  };

  /**
   * 打开新增弹窗
   */
  const handleAdd: () => void = () => {
    setDataObj(undefined);
    setModalMode('add');
    setModalVisible(true);
  };

  /**
   * 打开编辑弹窗
   */
  const handleEdit: (record: AssessmentPersonnel) => void = (record: AssessmentPersonnel) => {
    setDataObj(record);
    setModalMode('edit');
    setModalVisible(true);
  };

  /**
   * 打开查看弹窗
   */
  const handleView: (record: AssessmentPersonnel) => void = (record: AssessmentPersonnel) => {
    setDataObj(record);
    setModalMode('view');
    setModalVisible(true);
  };

  /**
   * 获取弹窗标题
   */
  const getModalTitle: () => string = () => {
    switch (modalMode) {
      case 'add':
        return '新增考核安排';
      case 'edit':
        return '编辑考核安排';
      default:
        return '查看考核安排';
    }
  };

  /**
   * 删除记录
   */
  const handleDelete: (id: string) => Promise<void> = async (id: string) => {
    try {
      const result: ApiResponse<boolean> = await assessmentApi.deleteAssessmentPersonnel(id);
      if (result.code === 200) {
        // 刷新列表
        if (actionRef.current) {
          actionRef.current.reload({});
        }
      }
    } catch {
      // console.error('删除失败:', error);
    }
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'planName',
      title: '计划名称',
      width: 200,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      ellipsis: true,
    },
    // {
    //   dataIndex: 'companyId',
    //   title: '公司名称',
    //   width: 180,
    //   query: true,
    //   display: true,
    //   componentName: 'PickData',
    //   componentProps: {
    //     requestData: async () => {
    //       try {
    //         const resData: Unit = await systemApi.unitTree();
    //         return formatTree(resData, 'unitType', 'unitName');
    //       } catch {
    //         return [];
    //       }
    //     },
    //     multiple: false,
    //     p_props: {
    //       placeholder: '请选择',
    //     },
    //   },
    //   render: (_v, record) => {
    //     return record.supplyUnit || '-';
    //   },
    // },
    {
      dataIndex: 'trainingType',
      title: '培训类型',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          return [
            { code: '1', text: '技能培训' },
            { code: '2', text: '安全培训' },
            { code: '3', text: '综合培训' },
            { code: '4', text: '专项培训' },
          ];
        },
        p_props: {
          placeholder: '请选择培训类型',
        },
      },
      render: (_v, record) => {
        return record.trainingTypeText || '-';
      },
    },
    {
      dataIndex: 'learningObject',
      title: '学习对象',
      width: 150,
      display: true,
      ellipsis: true,
    },
    {
      dataIndex: 'learningDuration',
      title: '学习时长',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'isExam',
      title: '是否考试',
      width: 100,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          return [
            { code: '1', text: '技能培训' },
            { code: '2', text: '安全培训' },
            { code: '3', text: '综合培训' },
            { code: '4', text: '专项培训' },
          ];
        },
        p_props: {
          placeholder: '请选择培训类型',
        },
      },
    },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="assessmentPersonnelList"
        action={listAction}
        searchMemory
        actionRef={actionRef}
        showRowSelection={false}
        operation={[
          {
            element: (
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增考核安排
              </Button>
            ),
          },
        ]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ApiResponse<AssessmentPersonnel[]> =
            await assessmentApi.queryAssessmentPersonnelList({
              aescs: [],
              descs: ['createDate'],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });

          if (resData.code && resData.code === 200) {
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={150}
        rowOperation={(row: AssessmentPersonnel) => {
          return [
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleView(row)}
                >
                  查看
                </Button>
              ),
            },
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(row)}
                >
                  编辑
                </Button>
              ),
            },
            {
              element: (
                <Popconfirm
                  title="确定要删除这条记录吗？"
                  onConfirm={() => handleDelete(row.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="link" size="small" danger icon={<DeleteOutlined />}>
                    删除
                  </Button>
                </Popconfirm>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title={getModalTitle()}
        width="80%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={modalVisible}
        key="assessment-personnel-modal"
      >
        <AssessmentPersonnelModal closeModal={closeModal} dataObj={dataObj} mode={modalMode} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  AssessmentPersonnelList,
  locales,
  YTHLocalization.getLanguage(),
);
